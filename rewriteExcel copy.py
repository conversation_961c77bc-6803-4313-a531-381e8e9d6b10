from openpyxl import load_workbook
import pandas as pd
import os
import msoffcrypto
from io import BytesIO
from tqdm import tqdm

# ========== 配置部分 ==========
REGISTERED_DIR = "已登记"
INPUT_FILE = "zhongdeng.xlsx"
SHEET_NAME = "放款主题明细"
OUTPUT_FILE = "test_data.xlsx"
EXCEL_PASSWORD = "123456abc"  # 所有“已登记”Excel文件的密码
# ==============================

# 清洗字符串（去除引号、空格等）
def clean_text(val):
    return str(val).strip().replace("’", "").replace("‘", "").replace("`", "").replace(" ", "")

# 解密 Excel 文件并返回 pandas DataFrame
def decrypt_excel(file_path, password):
    with open(file_path, "rb") as f:
        office_file = msoffcrypto.OfficeFile(f)
        office_file.load_key(password=password)
        decrypted = BytesIO()
        office_file.decrypt(decrypted)
        decrypted.seek(0)
        return pd.read_excel(decrypted, engine='openpyxl')

# 读取主表
df_main = pd.read_excel(INPUT_FILE, sheet_name=SHEET_NAME, engine='openpyxl')

# 准备写入结果的列（如果没有就创建）
if '登记编号' not in df_main.columns:
    df_main['登记编号'] = ''
if '修改码' not in df_main.columns:
    df_main['修改码'] = ''

# 缓存合同编号和车架号的匹配信息
contract_dict = {}
vin_dict = {}

# 扫描“已登记”目录下所有 Excel 文件，构建字典
print("正在预加载登记数据，请稍候...")
for filename in tqdm(os.listdir(REGISTERED_DIR)):
    if filename.endswith(".xlsx"):
        file_path = os.path.join(REGISTERED_DIR, filename)
        try:
            df = decrypt_excel(file_path, EXCEL_PASSWORD)
            for _, row in df.iterrows():
                # 确保索引在有效范围内
                if len(row) > 21:
                    contract_no = str(row.iloc[5]).strip()   # F列
                    vin = str(row.iloc[10]).strip()          # K列
                    reg_no = str(row.iloc[20]).strip()       # U列
                    mod_code = str(row.iloc[21]).strip()     # V列

                    # 只有当数据不为空时才添加到字典
                    if contract_no and contract_no != 'nan':
                        contract_dict[contract_no] = (reg_no, mod_code)
                    if vin and vin != 'nan':
                        vin_dict[vin] = (reg_no, mod_code)
        except Exception as e:
            print(f"文件 {filename} 解密或读取失败: {e}")

# 遍历主表每一行进行匹配
print("正在匹配并写入数据...")
for idx, row in tqdm(df_main.iterrows(), total=len(df_main)):
    # 修复：使用正确的列名访问方式
    subject_type = str(row.get('申请方种类', '')).strip()
    if subject_type != '自然人':
        continue

    # 修复：使用正确的列名访问方式
    is_registered = str(row.get('是否中重卡', '')).strip()
    matched = False

    if is_registered == '否':
        contract_no = str(row.get('合同编号', '')).strip()
        if contract_no in contract_dict:
            df_main.at[idx, '登记编号'] = clean_text(str(contract_dict[contract_no][0]))
            df_main.at[idx, '修改码'] = clean_text(str(contract_dict[contract_no][1]))
            matched = True
    elif is_registered == '是':
        vin = str(row.get('车架号', '')).strip()
        if vin in vin_dict:
            df_main.at[idx, '登记编号'] = clean_text(str(vin_dict[vin][0]))
            df_main.at[idx, '修改码'] = clean_text(str(vin_dict[vin][1]))
            matched = True

    if not matched:
        df_main.at[idx, '登记编号'] = "未找到"
        df_main.at[idx, '修改码'] = "未找到"

# 用 openpyxl 在原始 Excel 上写入，保留格式
print("正在保存结果文件（保留格式）...")
wb = load_workbook(INPUT_FILE)
ws = wb[SHEET_NAME]

# 读取表头行（第一行）
header_row = 1
headers = [str(cell.value).strip() if cell.value else '' for cell in ws[header_row]]

# 判断是否存在新列，如果没有就插入到表尾
added = False
if '登记编号' not in headers:
    ws.cell(row=header_row, column=len(headers) + 1, value='登记编号')
    added = True
if '修改码' not in headers:
    ws.cell(row=header_row, column=len(headers) + (2 if added else 1), value='修改码')

# 重新读取表头，刷新列索引
headers = [str(cell.value).strip() if cell.value else '' for cell in ws[header_row]]
col_T = headers.index('登记编号') + 1
col_U = headers.index('修改码') + 1

# 写入数据（从第二行开始）
for i, row in enumerate(ws.iter_rows(min_row=2, max_row=ws.max_row), start=0):
    ws.cell(row=i + 2, column=col_T).value = df_main.at[i, '登记编号']
    ws.cell(row=i + 2, column=col_U).value = df_main.at[i, '修改码']

# 保存为新文件
wb.save(OUTPUT_FILE)
print(f"处理完成，结果已保存为：{OUTPUT_FILE}")
