# 中登网注销系统 - 列标题使用说明

## 改进内容

原代码使用固定的列位置索引（如 `row_data[0]`, `row_data[1]` 等）来访问Excel数据，这种方式可读性差且难以维护。现在已改进为通过列标题名称来访问数据。

## 主要改进

### 1. 新增函数

- `get_column_value(row_data, headers, column_name)`: 通过列名获取行数据中的值
- `get_area_code(auth_token, row_data, headers)`: 获取省市区编码（已更新为支持列名）

### 2. 数据加载改进

`load_data()` 函数现在会：
- 读取Excel第一行作为列标题
- 创建列名到列索引的映射字典
- 返回headers字典供后续使用

### 3. 列标题映射

程序会自动检测Excel文件第一行的列标题，并创建以下映射关系：

#### 列名映射关系：

程序内部使用标准列名，但会自动映射到Excel文件中的实际列名：

**标准列名 → 实际Excel列名：**
- `登记期限` → `实际贷款期数`
- `证件号码` → `客户证件号码`
- `省份` → `居住地（省）`
- `城市` → `居住地（城市）`
- `地址` → `借款人户籍地址`
- `主合同编号` → `合同编号`
- `担保金额` → `裸车价`
- `债权开始日期` → `合同开始时间`
- `债权结束日期` → `原始到期日期`
- `担保物描述` → `车辆品牌`
- `车架号` → `车架号`
- `债务人类型` → `申请方种类`
- `登记编号` → `登记编号`
- `修改码` → `修改码`

**企业相关映射：**
- `企业省份` → `居住地（省）`
- `企业城市` → `居住地（城市）`
- `企业地址` → `借款人户籍地址`
- `企业名称` → `客户姓名`
- `法人代表` → `客户姓名`
- `行业类别` → `业务类型`
- `企业规模` → `业务类型`

## 使用方法

### 1. 准备Excel文件

确保你的Excel文件（test_data.xlsx）第一行包含实际的列标题。例如：

```
| 申请方种类 | 合同编号 | 合同开始时间 | 实际贷款期数 | 车辆品牌 | 车架号 | 客户证件号码 | 居住地（省） | 居住地（城市） | 裸车价 | ... |
|-----------|---------|-------------|-------------|---------|-------|-------------|-------------|---------------|--------|-----|
| 自然人     | ABC123  | 2024-01-01  | 12          | 东风    | VIN123| 123456789   | 北京市       | 朝阳区         | 100000 | ... |
```

**重要：** 你的Excel文件应该包含以下实际列名：
- `申请方种类`、`合同编号`、`合同开始时间`、`实际贷款期数`
- `车辆品牌`、`车架号`、`客户证件号码`
- `居住地（省）`、`居住地（城市）`、`借款人户籍地址`
- `裸车价`、`原始到期日期`、`登记编号`、`修改码`

### 2. 运行程序

程序启动时会显示检测到的列标题：

```
读取数据成功
检测到的列标题: ['登记期限', '证件号码', '省份', '城市', '地址', ...]
```

### 3. 错误处理

如果某个必需的列标题不存在，`get_column_value()` 函数会返回 `None`，程序会相应处理这种情况。

## 优势

1. **可读性提升**: 代码中使用 `get_column_value(row_data, headers, '登记期限')` 比 `row_data[0]` 更清晰
2. **易于维护**: 如果Excel列顺序改变，只需修改列标题，无需修改代码
3. **错误减少**: 避免因列位置变化导致的数据错位问题
4. **灵活性**: 支持动态列顺序，Excel文件可以有不同的列排列

## 注意事项

1. 确保Excel文件第一行包含正确的列标题
2. 列标题名称必须与代码中定义的完全一致（区分大小写）
3. 如果某些列不存在，程序会使用默认值或跳过相应处理

## 兼容性

- 如果Excel文件中缺少某些列标题，程序会使用默认的列索引作为备选
- 程序会在启动时显示检测到的所有列标题，便于调试
