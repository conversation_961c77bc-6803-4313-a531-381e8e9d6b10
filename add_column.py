#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在Excel文件的最后一列后面添加"注销结果"列
"""

import openpyxl
from openpyxl import load_workbook

def add_cancellation_result_column(file_path="test_data.xlsx"):
    """
    在Excel文件的最后一列后面添加"注销结果"列
    
    Args:
        file_path: Excel文件路径，默认为 test_data.xlsx
    """
    try:
        # 加载Excel文件
        print(f"正在加载文件: {file_path}")
        workbook = load_workbook(file_path)
        sheet = workbook.active
        
        # 获取当前的最大列数
        max_col = sheet.max_column
        max_row = sheet.max_row
        
        print(f"当前文件有 {max_row} 行，{max_col} 列")
        
        # 显示当前的列标题
        print("当前的列标题:")
        current_headers = []
        for col in range(1, max_col + 1):
            header_value = sheet.cell(1, col).value
            current_headers.append(header_value)
            print(f"  第{col}列: {header_value}")
        
        # 检查最后一列是否是"修改码"
        last_column_header = sheet.cell(1, max_col).value
        print(f"\n最后一列的标题是: '{last_column_header}'")
        
        # 在最后一列后面添加新列
        new_col = max_col + 1
        new_column_header = "注销结果"
        
        print(f"正在第{new_col}列添加新列标题: '{new_column_header}'")
        
        # 设置新列的标题
        sheet.cell(1, new_col).value = new_column_header
        
        # 确保新列的数据行都为空（实际上默认就是空的，但我们可以显式设置）
        # 这里不需要设置，因为新列默认就是空的
        
        # 显示添加后的列标题
        print(f"\n添加新列后的列标题:")
        for col in range(1, new_col + 1):
            header_value = sheet.cell(1, col).value
            print(f"  第{col}列: {header_value}")
        
        # 保存文件
        print(f"\n正在保存文件: {file_path}")
        workbook.save(file_path)
        
        print(f"✓ 成功在第{new_col}列添加了'{new_column_header}'列")
        print(f"✓ 文件已保存: {file_path}")
        print(f"✓ 新列的所有数据行保持为空")
        
        return True
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        return False
    except Exception as e:
        print(f"错误: 处理文件时发生异常: {e}")
        return False

def verify_column_addition(file_path="test_data.xlsx"):
    """
    验证新列是否正确添加
    
    Args:
        file_path: Excel文件路径
    """
    try:
        print(f"\n=== 验证文件 {file_path} ===")
        workbook = load_workbook(file_path)
        sheet = workbook.active
        
        max_col = sheet.max_column
        max_row = sheet.max_row
        
        print(f"文件现在有 {max_row} 行，{max_col} 列")
        
        # 检查最后一列是否是"注销结果"
        last_column_header = sheet.cell(1, max_col).value
        if last_column_header == "注销结果":
            print("✓ 新列'注销结果'已正确添加在最后一列")
        else:
            print(f"✗ 最后一列不是'注销结果'，而是'{last_column_header}'")
            return False
        
        # 检查新列的前几行数据是否为空
        print("检查新列的前5行数据:")
        for row in range(2, min(7, max_row + 1)):  # 检查第2-6行（跳过标题行）
            cell_value = sheet.cell(row, max_col).value
            print(f"  第{row}行: {cell_value if cell_value is not None else '(空)'}")
        
        # 显示所有列标题
        print("\n所有列标题:")
        for col in range(1, max_col + 1):
            header_value = sheet.cell(1, col).value
            print(f"  第{col}列: {header_value}")
        
        return True
        
    except Exception as e:
        print(f"验证时发生错误: {e}")
        return False

if __name__ == "__main__":
    print("=== 在Excel文件中添加'注销结果'列 ===")
    
    # 添加新列
    success = add_cancellation_result_column()
    
    if success:
        # 验证添加结果
        verify_column_addition()
    else:
        print("添加列失败")
