from datetime import datetime,timedelta, date
from dateutil.relativedelta import relativedelta
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By
from selenium.webdriver.support.select import Select
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions as EC
import openpyxl 
import requests
import time, random
import json
import cpca
import pandas as pd
import os

username = 'DFAFC029'
password = 'Qwer1234!!!'

base_headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,zh-TW;q=0.8",
        "Connection": "keep-alive",
        "Host": "www.zhongdengwang.org.cn",
        "Origin": "https://www.zhongdengwang.org.cn",
        "Referer": "https://www.zhongdengwang.org.cn/out/?",
        "Sec-Ch-Ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": '"Windows"',
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }

base_payload = {
        
    }

scale_dict = {
    '大型': '10',
    '中型': '20',
    '小型': '30',
    '微型': '40'
}
industryCode_dict = {
    '农、林、牧、渔业': 'A',
    '采矿业': 'B',
    '制造业': 'C',
    '电力、热力、燃气及水生产和供应业': 'D',
    '建筑业': 'E',
    '批发和零售业': 'F',
    '交通运输、仓储和邮政业': 'G',
    '住宿和餐饮业': 'H',
    '信息传输、软件和信息技术服务业': 'I',
    '金融业': 'J',
    '房地产业': 'K',
    '租赁和商务服务业': 'L',
    '科学研究和技术服务业': 'M',
    '水利、环境和公共设施管理业': 'N',
    '居民服务、修理和其他服务业': 'O',
    '教育': 'P',
    '卫生和社会工作': 'Q',
    '文化、体育和娱乐业': 'R',
    '公共管理、社会保障和社会组织': 'S',
    '国际组织': 'T',
    '其他': '9999'
}

browser_closed = False
driver = None

def send_requset(url, type, extra_headers=None, extra_payload=None):
    # 字典解包合并
    headers = {**base_headers, **(extra_headers or {})}
    payload = {**base_payload, **(extra_payload or {})}

    # print("headers:", headers)
    # print("payload:", payload)

    if type == 'json':
        response = requests.post(url, headers=headers, json=payload)
    elif type == 'data':
        response = requests.post(url, headers=headers, data=payload)
    else:
        raise ValueError(f"Unsupported type: {type}")
    return response

def load_data():
    # 读取excel
    workbook = openpyxl.load_workbook("test_data.xlsx") 
    sheet1 = workbook.active 
    row_count = sheet1.max_row
    saved_name = 'zhongdeng_zhuxiao_'+ date.today().strftime('%y%m%d') + '.xlsx'
    return workbook, sheet1, row_count, saved_name



def login():
    global driver, browser_closed
    browser_closed = False
    print("正在打开Chrome浏览器...")

    service = Service('./chromedriver_137.0.7151.119_x64.exe')
    # 使用chrome登录
    driver = webdriver.Chrome(service=service)
    # 打开中信网
    driver.get("https://www.zhongdengwang.org.cn/")

    # 全屏窗口
    driver.maximize_window()

    # 输入用户名 密码 验证码
    driver.find_element(By.ID,'usercode').send_keys(username)
    driver.find_element(By.ID,'password').send_keys(password)
    # driver.find_element(By.ID,'usercode').send_keys('DFAFC010')
    # driver.find_element(By.ID,'password').send_keys('Qwer#1234')

    # 启动后台线程监听浏览器是否被关闭
    import threading
    def check_browser_closed():
        global browser_closed
        while True:
            try:
                # 如果浏览器窗口句柄为空，说明已关闭
                if len(driver.window_handles) == 0:
                    browser_closed = True
                    print("检测到浏览器已关闭，等待当前操作完成...")
                    break
            except:
                browser_closed = True
                print("浏览器连接异常，可能已关闭。")
                break
            time.sleep(1)
    
    threading.Thread(target=check_browser_closed, daemon=True).start()

    input("请手动登录完成后，按回车继续...")

    if browser_closed:
        print("浏览器关闭，程序已结束")
        return None

    # 输入手机验证码
    print('登录成功')
    time.sleep(1)

    # 假设 driver 是你的 selenium 浏览器对象
    print('获取token...')
    auth_token = driver.execute_script("return window.sessionStorage.getItem('authToken');")
    return auth_token

def get_form_id(auth_token, row_data):
    headers = {
        "accessToken": auth_token,
        "Content-Type": "application/x-www-form-urlencoded"
    }

    payload = {
        "businessType": "B00000"
    }

    # 发送请求
    url = "https://www.zhongdengwang.org.cn/api/reg/initRegister/index"
    try:
        response = send_requset(url, extra_headers=headers, extra_payload=payload, type='data')
    except Exception as e:
        log_msg(reason="get_form_id()网络请求失败", row_data=row_data, response=None, exception=e)
        return None

    try:
        data = response.json()
    except Exception as e:
        log_msg(reason="get_form_id()解析 JSON 失败", row_data=row_data, response=response, exception=e)
        return None

    try:
        validateFlownNo = data['data']['validateFlownNo']
        return validateFlownNo
    except Exception as e:
        log_msg(reason="get_form_id()提取字段失败", row_data=row_data, response=response, exception=e)
        return None

def send_form(auth_token, form_id, row_data, province_code, city_code):
    # print("row_data:",row_data)
    headers = {
        "accessToken": auth_token,
        "Content-Type": "application/json"
    }
    payload = {}
    if row_data[18] == '自然人':
        # print('自然人')
        payload = {
            "changeItems": [
                "1",
                "2",
                "3"
            ],
            "businessType": "B00000",
            "subBusinessType": "",
            "mortgageType": "1",
            "mainContractSum": "",
            "mainContractCurrency": "",
            "pledgeCollateralSum": row_data[6],
            "pledgeContractCurrency": "CNY",
            "tPropDesc": {
                "collateralDescribe": row_data[9] + " 车架号" + row_data[10],
                "maxAmountCreditGroup": "",
                "maxAmountCredit": "",
                "maxAmountCreditCurrency": ""
            },
            "payType": "2",
            "debtorInfo": [
                {
                    "id": None,
                    "debtorType": "04",
                    "certificateType": "01",
                    "address": row_data[4],
                    "nationality": "CHN",
                    "certificateTypeName": "身份证",
                    "certificateCode": row_data[1],
                    "city": city_code,
                    "provinceName": row_data[2],
                    "cityName": row_data[3],
                    "province": province_code,
                    "debtorTypeName": "个人"
                }
            ],
            "pawneeInfo": [
                {
                    "id": None,
                    "owenId": None,
                    "debtorType": "0116",
                    "debtorTypeName": "金融机构",
                    "debtorName": "东风汽车金融有限公司",
                    "certificateCode": None,
                    "userType": None,
                    "address": "朝阳区东三环中路24号楼20层、25层01、05单元",
                    "corporationName": "冯长军",
                    "num": None,
                    "certificateType": None,
                    "certificateTypeName": "",
                    "certificateExpirationDate": None,
                    "certificateExpirationDateName": None,
                    "distributeCountry": None,
                    "distributeCountryName": None,
                    "nationality": "CHN",
                    "province": "110000",
                    "provinceName": "北京市",
                    "city": "11XX00",
                    "cityName": "",
                    "tradeName": None,
                    "organizationCode": "91110000634988516G",
                    "finInstCode": "N0004H211000001",
                    "instCorpCertNo": None,
                    "orgRegisterCertNo": None,
                    "industryRegistrationCode": None,
                    "scale": None,
                    "scaleName": None,
                    "industryCode": None,
                    "industryCodeName": None,
                    "searchText": None,
                    "lei": None,
                    "orgCode": "91110000634988516G",
                    "isTimeOut": None,
                    "my": "1",
                    "pawneeName": "东风汽车金融有限公司",
                    "pawneeType": "0116",
                    "pawneeTypeName": "金融机构"
                }
            ],
            "propertyAttachments": [
                
            ],
            "propertyElements": [
                
            ],
            "mainContractGroup": "",
            "pledgeCollateralGroup": "[object Object],[object Object][object Object]",
            "debtDateGroup": row_data[7] + "[]" + row_data[8],
            "step": "1",
            "currentDate": datetime.today().strftime('%Y-%m-%d'),
            "userType": "11",
            "userName": "东风汽车金融有限公司",
            "certificateType": None,
            "certificateTypeName": None,
            "certificateCode": None,
            "addressStr": "北京市朝阳区东三环中路24号楼20层、25层01、05单元",
            "validateFlownNo": form_id,
            "registerLimit": row_data[0],
            "realExpireDate": get_date_plus_months_then_minus_one_day(row_data[0]),
            "mainContractNo": row_data[5],
            "debtBeginDate": row_data[7],
            "debtEndDate": row_data[8],
            "leaseMode": "02"
        }
    elif row_data[18] == '企业':
        # print('企业')
        payload = {
            "changeItems": [
                "1",
                "2",
                "3"
            ],
            "businessType": "B00000",
            "subBusinessType": "",
            "mortgageType": "1",
            "mainContractSum": "",
            "mainContractCurrency": "",
            "pledgeCollateralSum": row_data[6],
            "pledgeContractCurrency": "CNY",
            "tPropDesc": {
                "collateralDescribe": row_data[9] + " 车架号" + row_data[10],
                "maxAmountCreditGroup": "",
                "maxAmountCredit": "",
                "maxAmountCreditCurrency": ""
            },
            "payType": "2",
            "debtorInfo": [
                {
                    "id": None,
                    "debtorType": "02",
                    "certificateType": "",
                    "address": row_data[14],
                    "nationality": "CHN",
                    "debtorName": row_data[17],
                    "orgCode": row_data[1],
                    "corporationName": row_data[11],
                    "industryCode": industryCode_dict.get(row_data[12]),
                    "scale": scale_dict.get(row_data[13]),
                    "city": city_code,
                    "provinceName": row_data[15],
                    "cityName": row_data[16],
                    "province": province_code,
                    "debtorTypeName": "企业"
                }
            ],
            "pawneeInfo": [
                {
                    "id": None,
                    "owenId": None,
                    "debtorType": "0116",
                    "debtorTypeName": "金融机构",
                    "debtorName": "东风汽车金融有限公司",
                    "certificateCode": None,
                    "userType": None,
                    "address": "朝阳区东三环中路24号楼20层、25层01、05单元",
                    "corporationName": "冯长军",
                    "num": None,
                    "certificateType": None,
                    "certificateTypeName": "",
                    "certificateExpirationDate": None,
                    "certificateExpirationDateName": None,
                    "distributeCountry": None,
                    "distributeCountryName": None,
                    "nationality": "CHN",
                    "province": "110000",
                    "provinceName": "北京市",
                    "city": "11XX00",
                    "cityName": "",
                    "tradeName": None,
                    "organizationCode": "91110000634988516G",
                    "finInstCode": "N0004H211000001",
                    "instCorpCertNo": None,
                    "orgRegisterCertNo": None,
                    "industryRegistrationCode": None,
                    "scale": None,
                    "scaleName": None,
                    "industryCode": None,
                    "industryCodeName": None,
                    "searchText": None,
                    "lei": None,
                    "orgCode": "91110000634988516G",
                    "isTimeOut": None,
                    "my": "1",
                    "pawneeName": "东风汽车金融有限公司",
                    "pawneeType": "0116",
                    "pawneeTypeName": "金融机构"
                }
            ],
            "propertyAttachments": [
                
            ],
            "propertyElements": [
                
            ],
            "mainContractGroup": "",
            "pledgeCollateralGroup": "[object Object],[object Object][object Object]",
            "debtDateGroup": row_data[7] + "[]" + row_data[8],
            "step": "1",
            "currentDate": datetime.today().strftime('%Y-%m-%d'),
            "userType": "11",
            "userName": "东风汽车金融有限公司",
            "certificateType": None,
            "certificateTypeName": None,
            "certificateCode": None,
            "addressStr": "北京市朝阳区东三环中路24号楼20层、25层01、05单元",
            "validateFlownNo": form_id,
            "registerLimit": row_data[0],
            "realExpireDate": get_date_plus_months_then_minus_one_day(row_data[0]),
            "mainContractNo": row_data[5],
            "debtBeginDate": row_data[7],
            "debtEndDate": row_data[8],
            "leaseMode": "02"
        }
    else:
        log_msg(reason="send_form()非自然人或企业，操作失败", row_data=row_data, request={"Headers": headers, "Payload": payload})
        return None
        
    # 发送请求
    url = "https://www.zhongdengwang.org.cn/api/reg/initRegister/initRegisterPreview"
    try:
        response = send_requset(url, extra_headers=headers, extra_payload=payload, type='json')
    except Exception as e:
        log_msg(reason="send_form()网络请求失败", row_data=row_data, exception=e, request={"Headers": headers, "Payload": payload})
        return None
    # 验证是否成功
    try:
        resp_json = response.json()
        if (resp_json is not None and
            resp_json.get("code") == 1000 and
            resp_json.get("msg") == "数据校验成功" and
            resp_json.get("success") is True
        ):
            print("数据校验成功")
            log_msg(reason="send_form()发送、校验成功。", row_data=row_data, response=response, request={"Headers": headers, "Payload": payload})
            return '200'
        else:
            log_msg(reason="send_form()数据校验失败", row_data=row_data, response=response, request={"Headers": headers, "Payload": payload})
            return None
    except Exception as e:
        log_msg(reason="send_form()响应解析json失败", row_data=row_data, response=response, request={"Headers": headers, "Payload": payload}, Exception=e)
        return None

def log_msg(reason, row_data=None, response=None, exception=None, request=None):
    # 获取当前日期
    now = datetime.now()
    date_str = now.strftime('%Y-%m-%d')
    
    # 构造日志目录和文件路径
    log_dir = "log"
    os.makedirs(log_dir, exist_ok=True)  # 自动创建 log 目录（若不存在）
    log_file_path = os.path.join(log_dir, f"{date_str}_log.txt")

    with open(log_file_path, "a", encoding="utf-8") as f:
        f.write("\n" + "-" * 50 + f"[{now.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}] {reason}\n")
        if row_data:
            f.write(f"row_data: {row_data}\n")

        if request:
            headers = request.get("Headers")
            payload = request.get("Payload")
            if headers:
                f.write("Request Headers:\n")
                f.write(json.dumps(headers, ensure_ascii=False, indent=2) + "\n")
            if payload:
                f.write("Request Payload:\n")
                if isinstance(payload, (dict, list)):
                    f.write(json.dumps(payload, ensure_ascii=False, indent=2) + "\n")
                else:
                    f.write(str(payload) + "\n")
        
        if response:
            try:
                f.write("response JSON:\n")
                f.write(json.dumps(response.json(), ensure_ascii=False, indent=2))
            except Exception:
                f.write(f"response.text (非JSON): {response.text}\n")
        
        if exception:
            f.write(f"exception: {exception}\n")
        
        f.write("\n" + "-" * 200 + "\n")

def get_date_plus_months_then_minus_one_day(months_to_add):
    today = datetime.today()
    future_date = today + relativedelta(months=months_to_add)
    target_date = future_date - timedelta(days=1)
    return target_date.strftime('%Y-%m-%d')

def submit_form(auth_token, form_id, row_data):
    headers = {
        "accessToken": auth_token,
        "Content-Type": "application/json"
    }

    payload = {
        "validateFlownNo": form_id
    }

    # 发送请求
    url = "https://www.zhongdengwang.org.cn/api/reg/initRegister/saveOver"
    try:
        response = send_requset(url, extra_headers=headers, extra_payload=payload, type='json')
    except Exception as e:
        log_msg(reason="submit_form()网络请求失败", row_data=row_data, exception=e, request={"Headers": headers, "Payload": payload})
        return None
    
    try:
        data = response.json()
    except Exception as e:
        log_msg(reason="submit_form()解析 JSON 失败", row_data=row_data, response=response, exception=e, request={"Headers": headers, "Payload": payload})
        return None

    try:
        registerNo = data['data']['subRegistrationNo']
        registrationId = data['data']['registrationId']
        log_msg(reason="submit_form()提交成功", row_data=row_data, response=response, request={"Headers": headers, "Payload": payload})
        return registerNo
    except Exception as e:
        log_msg("submit_form()提取字段失败", payload, response=response, exception=e, request={"Headers": headers, "Payload": payload})
        return None
    
def get_modifyCode(auth_token, form_id, registerNo, row_data):
    headers = {
        "accessToken": auth_token,
        "Content-Type": "application/x-www-form-urlencoded"
    }

    payload = {
        "validateFlownNo": form_id,
        "registerNo": registerNo
    }

    # 发送请求
    url = "https://www.zhongdengwang.org.cn/api/reg/initRegister/saveResult"
    try:
        response = send_requset(url, extra_headers=headers, extra_payload=payload, type='data')
    except Exception as e:
        log_msg(reason="get_modifyCode()网络请求失败", row_data=row_data, exception=e, request={"Headers": headers, "Payload": payload})
        return None

    try:
        data = response.json()
    except Exception as e:
        log_msg(reason="get_modifyCode()解析 JSON 失败", row_data=row_data, response=response, exception=e, request={"Headers": headers, "Payload": payload})
        return None

    try:
        modifyCode = data['data']['modifyCode']
        log_msg(reason="获取修改码成功", row_data=row_data, response=response, request={"Headers": headers, "Payload": payload})
        return modifyCode
    except Exception as e:
        log_msg(reason="get_modifyCode()提取字段失败", row_data=row_data, response=response, exception=e, request={"Headers": headers, "Payload": payload})
        return None
    
def check_form(auth_token, form_id, row_data):
    headers = {
        "accessToken": auth_token,
        "Content-Type": "application/x-www-form-urlencoded"
    }

    payload = {
        "validateFlownNo": form_id
    }
    # 发送请求
    url = "https://www.zhongdengwang.org.cn/api/reg/initRegister/registerPreview"
    try:
        response = send_requset(url, extra_headers=headers, extra_payload=payload, type='data')
    except Exception as e:
        log_msg(reason="check_form()网络请求失败", row_data=row_data, exception=e, request={"Headers": headers, "Payload": payload})
        return

    try:
        data = response.json()
    except Exception as e:
        log_msg(reason="check_form()解析 JSON 失败", row_data=row_data, response=response, exception=e, request={"Headers": headers, "Payload": payload})
        return

    try:
        log_msg(reason="check_form()成功", row_data=row_data, response=response, request={"Headers": headers, "Payload": payload})
        return '200'
    except Exception as e:
        log_msg("check_form()提取字段失败", payload, response=response, exception=e, request={"Headers": headers, "Payload": payload})
        return

def main():
    global browser_closed

    # 读取数据
    print("读取数据...")
    workbook, sheet1, row_count, saved_name = load_data()
    print("读取数据成功")

    # 获取token
    auth_token = login()
    if browser_closed:
        print("浏览器关闭，程序已结束")
        return
    if not auth_token:
        print("获取token失败，请联系it")
        return
    # print(auth_token)
    print("获取token成功")

    try:
        success_nums = 0
        fail_nums = 0
        # 循环上传
        for i in range(2,row_count+1):
            if browser_closed:
                print("浏览器关闭，程序已结束")
                return
            
            # 获取当前行数据
            row_data = [sheet1.cell(i,j).value for j in range(1,sheet1.max_column+1)]
            if row_data[20] or row_data[21]:
                print(f"第{i}行已有登记编号或修改码，跳过")
                continue

            # 获取表单id
            wait_time = random.randint(5, 7)
            print(f"等待{wait_time}秒，点击融资租赁")
            time.sleep(wait_time)
            if browser_closed:
                print("浏览器关闭，程序已结束")
                return
            print("点击融资租赁获取表单id...")
            form_id = get_form_id(auth_token, row_data)
            if form_id:
                print("获取表单id成功")

                # 获取行政区划
                print("获取行政区划代码...")
                province_code, city_code = get_area_code(auth_token, row_data)
                # 获取失败就写入-1，跳过此条数据
                if province_code is None or city_code is None:
                    print("获取省市区编码失败,写入-1，跳过此条数据")
                    sheet1.cell(i, 21).value = -1
                    sheet1.cell(i, 22).value = -1
                    workbook.save(saved_name)
                    fail_nums += 1
                    continue
                print("省编码：" + province_code + " 市编码：" + city_code)
                # 填写数据，点击预览
                wait_time = random.randint(25, 30)
                print(f"等待{wait_time}秒，预览表单进行校验")
                time.sleep(wait_time)
                if browser_closed:
                    print("浏览器关闭，程序已结束")
                    return
                print("预览表单")
                code = send_form(auth_token, form_id, row_data, province_code, city_code)
                # print(f"send_form() 返回 code: {repr(code)}，类型: {type(code)}")
                if str(code).strip() != '200':
                    print("表单预览失败，请联系it")
                    break

                # 预览会再次发送请求，中登服务器返回表单信息，执行没必要，可记录入日志，方便后续追踪
                if browser_closed:
                    print("浏览器关闭，程序已结束")
                    return
                code = check_form(auth_token, form_id, row_data)
                if str(code).strip() == '200':
                    print("检查预览成功")
                else:
                    print("检查预览失败，请联系it")
                    break
                
                # 点击提交，获取中登网登记编号
                wait_time = random.randint(5, 8)
                print(f"等待{wait_time}秒，提交表单")
                time.sleep(wait_time)
                if browser_closed:
                    print("浏览器关闭，程序已结束")
                    return
                print("提交表单，获取登记编号,《《《此过程请勿结束程序》》》")
                registerNo = submit_form(auth_token, form_id, row_data)
                if registerNo == None:
                    print("获取登记编号失败，请联系it")
                    break
                print("获取登记编号成功,《《《此过程请勿结束程序》》》")

                # 获取修改码
                modifyCode = get_modifyCode(auth_token, form_id, registerNo, row_data)
                if modifyCode == None:
                    print("获取修改码失败，请联系it")
                    break
                print("获取修改码成功,《《《此过程请勿结束程序》》》")

                # 写入saved_name表格
                print("写入表格,《《《此过程请勿结束程序》》》")
                sheet1.cell(i, 21).value = registerNo
                sheet1.cell(i, 22).value = modifyCode
                workbook.save(saved_name)
                success_nums += 1
                print("写入成功")

                if browser_closed:
                    print("浏览器关闭，程序已结束")
                    return

                wait_time = random.randint(5, 7)
                print(f"登记进度{i-1}/{row_count}。已执行{success_nums+fail_nums}条数据，成功{success_nums}条，失败{fail_nums}条。等待{wait_time}秒，继续下一条数据")
                print("-" * 80)

                if browser_closed:
                    print("浏览器关闭，程序已结束")
                    return

                time.sleep(wait_time)
    finally:
        print("执行完毕")
        try:
            workbook.save(saved_name)
            print("文件已保存")
        except Exception as e:
            print(f"退出时保存失败：{e}")


if __name__ == "__main__":
    main()